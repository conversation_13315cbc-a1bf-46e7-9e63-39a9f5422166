<!DOCTYPE html>
<html lang="en">
    <head>
        <meta charset="UTF-8" />
        <meta http-equiv="X-UA-Compatible" content="IE=edge,chrome=1" />
        <meta name="viewport" content="width=device-width, minimal-ui, viewport-fit=cover, initial-scale=1.0, maximum-scale=1.0, minimum-scale=1.0, user-scalable=no" />
        <link rel="icon" type="image/png" href="assets/favicon.png" />

        <title>OGL • Performance - High mesh count</title>
        <link href="assets/main.css" rel="stylesheet" />
    </head>
    <body>
        <!-- 页面标题显示 -->
        <div class="Info">Performance - High mesh count</div>
        <!-- 用户交互界面：输入框和按钮用于设置网格数量 -->
        <div class="Info" style="padding-top: 0">
            <input id="meshCountInput" style="width: 100px"></input>
            <button  id="meshCountButton" style="padding:2px; border: 1px solid #000; background-color:#fff;" onclick="setMeshCount(document.getElementById('meshCountInput').value)">Set mesh count</button>
        </div>
        <script type="module">
            // 导入OGL库的核心组件
            // Renderer: WebGL渲染器
            // Camera: 相机对象，用于定义视角和投影
            // Transform: 变换对象，用于场景图管理
            // Program: 着色器程序，包含顶点和片段着色器
            // Mesh: 网格对象，结合几何体和材质
            // Box: 立方体几何体
            import { Renderer, Camera, Transform, Program, Mesh, Box } from '../src/index.js';

            // 顶点着色器代码 (GLSL)
            // 负责处理每个顶点的位置变换和法线计算
            const vertex = /* glsl */ `
                // 输入属性：顶点位置和法线向量
                attribute vec3 position;
                attribute vec3 normal;

                // 统一变量：变换矩阵
                uniform mat4 modelViewMatrix;    // 模型视图矩阵（模型变换 * 视图变换）
                uniform mat4 projectionMatrix;   // 投影矩阵（透视或正交投影）
                uniform mat3 normalMatrix;       // 法线矩阵（用于正确变换法线）

                // 输出变量：传递给片段着色器的法线
                varying vec3 vNormal;

                void main() {
                    // 将法线从模型空间变换到世界空间，并传递给片段着色器
                    vNormal = normalize(normalMatrix * normal);

                    // 计算最终的顶点位置：投影矩阵 * 模型视图矩阵 * 顶点位置
                    gl_Position = projectionMatrix * modelViewMatrix * vec4(position, 1.0);
                }
            `;

            // 片段着色器代码 (GLSL)
            // 负责计算每个像素的最终颜色
            const fragment = /* glsl */ `
                // 设置浮点精度为高精度
                precision highp float;

                // 从顶点着色器接收的法线向量
                varying vec3 vNormal;

                void main() {
                    // 归一化法线向量（插值后可能不再是单位向量）
                    vec3 normal = normalize(vNormal);

                    // 简单的方向光照计算
                    // 光源方向：(-0.3, 0.8, 0.6) 归一化后的向量
                    // 使用点积计算光照强度
                    float lighting = dot(normal, normalize(vec3(-0.3, 0.8, 0.6)));

                    // 设置基础颜色（浅蓝色）并添加光照效果
                    gl_FragColor.rgb = vec3(0.2, 0.8, 1.0) + lighting * 0.1;
                    gl_FragColor.a = 1.0;  // 完全不透明
                }
            `;

            {
                // === WebGL 渲染器初始化 ===
                // 创建WebGL渲染器实例
                const renderer = new Renderer();
                const gl = renderer.gl;  // 获取WebGL上下文

                // 将canvas添加到页面body中
                document.body.appendChild(gl.canvas);

                // 设置清除颜色为白色 (R=1, G=1, B=1, A=1)
                gl.clearColor(1, 1, 1, 1);

                // === 相机设置 ===
                // 创建透视相机
                // fov: 35度视野角度
                // far: 3000单位的远裁剪面（用于大场景渲染）
                const camera = new Camera(gl, { fov: 35, far: 3000 });

                // === 窗口大小调整处理 ===
                function resize() {
                    // 设置渲染器尺寸为窗口大小
                    renderer.setSize(window.innerWidth, window.innerHeight);

                    // 更新相机的宽高比以匹配新的窗口尺寸
                    camera.perspective({ aspect: gl.canvas.width / gl.canvas.height });
                }

                // 监听窗口大小变化事件
                window.addEventListener('resize', resize, false);
                resize();  // 初始化时调用一次

                // === 场景图根节点 ===
                // Transform对象作为场景图的根节点，所有网格都将添加到这里
                const scene = new Transform();

                // === 几何体和着色器程序创建 ===
                // 创建立方体几何体（所有网格共享同一个几何体以节省内存）
                const cubeGeometry = new Box(gl);

                // 创建着色器程序，使用上面定义的顶点和片段着色器
                const program = new Program(gl, {
                    vertex,
                    fragment,
                });

                // === 网格容器 ===
                // 存储所有创建的网格对象的数组
                let meshes = [];

                // === 网格数量设置函数 ===
                // 全局函数，用于动态设置场景中的网格数量
                // 这个函数被HTML按钮的onclick事件调用
                window.setMeshCount = function setMeshCount(count) {
                    // === 输入验证 ===
                    // 将输入转换为整数，如果无效则默认为1000
                    count = parseInt(count) || 1000;

                    // === 清理旧网格 ===
                    // 从场景中移除所有现有的网格对象
                    for (let i = 0; i < meshes.length; ++i) {
                        scene.removeChild(meshes[i]);
                    }
                    meshes = [];  // 清空网格数组

                    // === 创建新网格 ===
                    // 根据指定数量创建新的网格对象
                    for (let i = 0; i < count; ++i) {
                        // 创建网格实例，共享同一个几何体和着色器程序
                        // 这种共享方式可以显著提高性能和减少内存使用
                        let mesh = new Mesh(gl, { geometry: cubeGeometry, program });

                        // === 随机位置设置 ===
                        // 在每个轴上设置-100到+100之间的随机位置
                        // 这创建了一个200x200x200单位的立方体区域
                        mesh.position.set(
                            -100 + Math.random() * 200,  // X轴随机位置
                            -100 + Math.random() * 200,  // Y轴随机位置
                            -100 + Math.random() * 200   // Z轴随机位置
                        );

                        // === 随机旋转设置 ===
                        // 为每个网格设置随机的初始旋转角度（0-3弧度）
                        mesh.rotation.set(
                            Math.random() * 3,  // X轴旋转
                            Math.random() * 3,  // Y轴旋转
                            Math.random() * 3   // Z轴旋转
                        );

                        // 将网格添加到场景图中
                        scene.addChild(mesh);

                        // 将网格添加到数组中以便后续管理
                        meshes.push(mesh);
                    }

                    // === 更新UI ===
                    // 确保输入框显示实际设置的网格数量
                    document.getElementById('meshCountInput').value = count;
                };

                // === 初始化 ===
                // 默认创建1000个网格对象
                setMeshCount(1000);

                // === 动画循环启动 ===
                requestAnimationFrame(update);

                // === 主渲染循环函数 ===
                function update() {
                    // 请求下一帧动画，创建60FPS的动画循环
                    requestAnimationFrame(update);

                    // === 相机动画 ===
                    // 基于时间的相机轨道运动
                    let time = performance.now() / 30000;  // 将毫秒转换为缓慢变化的时间值

                    // 相机围绕原点做圆周运动
                    // 半径为180单位，高度固定在80单位
                    camera.position.set(
                        Math.sin(time) * 180,  // X轴位置（圆周运动）
                        80,                     // Y轴位置（固定高度）
                        Math.cos(time) * 180   // Z轴位置（圆周运动）
                    );

                    // 相机始终朝向场景中心点(0,0,0)
                    camera.lookAt([0, 0, 0]);

                    // === 网格动画 ===
                    // 为所有网格添加持续的旋转动画
                    for (let i = 0; i < meshes.length; ++i) {
                        // 每帧在X和Y轴上增加0.01弧度的旋转
                        // 这创建了平滑的旋转动画效果
                        meshes[i].rotation.x += 0.01;
                        meshes[i].rotation.y += 0.01;
                    }

                    // === 渲染场景 ===
                    // 使用当前的场景和相机状态进行渲染
                    // 这是每帧都会执行的核心渲染调用
                    renderer.render({ scene, camera });
                }
            }
        </script>
    </body>
</html>

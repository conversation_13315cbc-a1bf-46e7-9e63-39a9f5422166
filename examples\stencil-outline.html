<!DOCTYPE html>
<html lang="en">
    <head>
        <meta charset="UTF-8" />
        <meta http-equiv="X-UA-Compatible" content="IE=edge,chrome=1" />
        <meta name="viewport" content="width=device-width, minimal-ui, viewport-fit=cover, initial-scale=1.0, maximum-scale=1.0, minimum-scale=1.0, user-scalable=no" />
        <link rel="icon" type="image/png" href="assets/favicon.png" />

        <title>OGL • Stencil Outline</title>
        <link href="assets/main.css" rel="stylesheet" />
    </head>
    <body>
        <div class="Info">
            Stencil Outline - 模版缓冲区轮廓渲染<br />
            <small
                >使用模版缓冲区实现物体轮廓效果<br />
                拖拽鼠标旋转视角，滚轮缩放，右键平移</small
            >
        </div>
        <script type="module">
            import { Renderer, Program, Color, Mesh, Box, Camera, Transform, Vec3, Orbit } from '../src/index.js';

            // 基础顶点着色器
            const vertex = /* glsl */ `
                attribute vec3 position;
                attribute vec3 normal;

                uniform mat4 modelViewMatrix;
                uniform mat4 projectionMatrix;
                uniform mat3 normalMatrix;
                uniform float uOutlineWidth;

                varying vec3 vNormal;

                void main() {
                    vNormal = normalize(normalMatrix * normal);
                    
                    // 对于轮廓，沿法线方向扩展顶点
                    vec3 pos = position + normal * uOutlineWidth;
                    gl_Position = projectionMatrix * modelViewMatrix * vec4(pos, 1.0);
                }
            `;

            // 物体着色器
            const objectFragment = /* glsl */ `
                precision highp float;

                uniform vec3 uColor;
                
                varying vec3 vNormal;

                void main() {
                    // 简单的漫反射光照
                    vec3 lightDir = normalize(vec3(1.0, 1.0, 1.0));
                    float diff = max(dot(vNormal, lightDir), 0.0);
                    
                    vec3 color = uColor * (0.3 + 0.7 * diff);
                    gl_FragColor = vec4(color, 1.0);
                }
            `;

            // 轮廓着色器
            const outlineFragment = /* glsl */ `
                precision highp float;

                uniform vec3 uOutlineColor;

                void main() {
                    gl_FragColor = vec4(uOutlineColor, 1.0);
                }
            `;

            {
                const renderer = new Renderer({
                    stencil: true,
                });
                const gl = renderer.gl;
                document.body.appendChild(gl.canvas);
                gl.clearColor(0.1, 0.1, 0.2, 1);

                // 创建相机
                const camera = new Camera(gl, { fov: 45 });
                camera.position.set(0, 0, 5);

                // 创建轨道控制器
                const controls = new Orbit(camera, {
                    target: new Vec3(0, 0, 0),
                });

                function resize() {
                    renderer.setSize(window.innerWidth, window.innerHeight);
                    camera.perspective({ aspect: gl.canvas.width / gl.canvas.height });
                }
                window.addEventListener('resize', resize, false);
                resize();

                // 创建几何体
                const boxGeometry = new Box(gl);

                // 创建物体程序
                const objectProgram = new Program(gl, {
                    vertex,
                    fragment: objectFragment,
                    uniforms: {
                        uColor: { value: new Color(0.8, 0.3, 0.5) },
                        uOutlineWidth: { value: 0.0 }, // 物体本身不需要轮廓扩展
                    },
                });

                // 设置物体的模版测试（写入模版缓冲区）
                objectProgram.setStencilFunc(gl.ALWAYS, 1, 0xff);
                objectProgram.setStencilOp(gl.KEEP, gl.KEEP, gl.REPLACE);

                // 创建轮廓程序
                const outlineProgram = new Program(gl, {
                    vertex,
                    fragment: outlineFragment,
                    uniforms: {
                        uColor: { value: new Color(0.8, 0.3, 0.5) }, // 保持一致性
                        uOutlineColor: { value: new Color(1.0, 1.0, 0.0) }, // 黄色轮廓
                        uOutlineWidth: { value: 0.05 }, // 轮廓宽度
                    },
                    cullFace: gl.FRONT, // 只渲染背面，避免Z-fighting
                    depthFunc: gl.LEQUAL,
                });

                // 设置轮廓的模版测试（只在物体外部渲染）
                outlineProgram.setStencilFunc(gl.NOTEQUAL, 1, 0xff);
                outlineProgram.setStencilOp(gl.KEEP, gl.KEEP, gl.KEEP);

                // 创建场景对象
                const scene = new Transform();

                // 创建物体（立方体）
                const cube = new Mesh(gl, { geometry: boxGeometry, program: objectProgram });
                cube.setParent(scene);

                // 创建轮廓物体
                const outlineCube = new Mesh(gl, { geometry: boxGeometry, program: outlineProgram });
                outlineCube.setParent(scene);

                requestAnimationFrame(update);
                function update(t) {
                    requestAnimationFrame(update);

                    const time = t * 0.001;

                    // 更新轨道控制器
                    controls.update();

                    // 旋转立方体
                    cube.rotation.x = time * 0.5;
                    cube.rotation.y = time * 0.7;

                    // 同步轮廓立方体的变换
                    outlineCube.rotation.copy(cube.rotation);
                    outlineCube.position.copy(cube.position);
                    outlineCube.scale.copy(cube.scale);

                    // 清除缓冲区
                    gl.clear(gl.COLOR_BUFFER_BIT | gl.DEPTH_BUFFER_BIT | gl.STENCIL_BUFFER_BIT);

                    // 第一步：渲染轮廓（扩展的物体）
                    renderer.render({ scene: outlineCube, camera });

                    // 第二步：渲染正常物体（会写入模版缓冲区）
                    renderer.render({ scene: cube, camera, clear: false });
                }
            }
        </script>
    </body>
</html>

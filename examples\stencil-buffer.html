<!DOCTYPE html>
<html lang="en">
    <head>
        <meta charset="UTF-8" />
        <meta http-equiv="X-UA-Compatible" content="IE=edge,chrome=1" />
        <meta name="viewport" content="width=device-width, minimal-ui, viewport-fit=cover, initial-scale=1.0, maximum-scale=1.0, minimum-scale=1.0, user-scalable=no" />
        <link rel="icon" type="image/png" href="assets/favicon.png" />

        <title>OGL • Stencil Buffer</title>
        <link href="assets/main.css" rel="stylesheet" />
    </head>
    <body>
        <div class="Info">
            Stencil Buffer - 模版缓冲区示例<br />
            <small
                >展示模版缓冲区的基本用法：创建遮罩和应用遮罩效果<br />
                拖拽鼠标旋转视角，滚轮缩放，右键平移</small
            >
        </div>
        <script type="module">
            import { Renderer, Program, Color, Mesh, Plane, Box, Camera, Transform, Vec3, Orbit } from '../src/index.js';

            // 基础顶点着色器
            const vertex = /* glsl */ `
                attribute vec3 position;
                attribute vec2 uv;

                uniform mat4 modelViewMatrix;
                uniform mat4 projectionMatrix;

                varying vec2 vUv;

                void main() {
                    vUv = uv;
                    gl_Position = projectionMatrix * modelViewMatrix * vec4(position, 1.0);
                }
            `;

            // 遮罩着色器（用于写入模版缓冲区）
            const maskFragment = /* glsl */ `
                precision highp float;
                
                void main() {
                    // 简单的白色，主要目的是写入模版缓冲区
                    gl_FragColor = vec4(1.0, 1.0, 1.0, 1.0);
                }
            `;

            // 彩色着色器（用于渲染彩色内容）
            const colorFragment = /* glsl */ `
                precision highp float;

                uniform float uTime;
                uniform vec3 uColor;
                
                varying vec2 vUv;

                void main() {
                    // 创建动态彩色效果
                    vec3 color = uColor;
                    color += 0.3 * cos(vUv.xyx * 10.0 + uTime);
                    
                    gl_FragColor = vec4(color, 1.0);
                }
            `;

            // 背景着色器
            const backgroundFragment = /* glsl */ `
                precision highp float;

                uniform float uTime;
                
                varying vec2 vUv;

                void main() {
                    // 创建动态背景效果
                    vec2 uv = vUv * 2.0 - 1.0;
                    float d = length(uv);
                    vec3 color = vec3(0.1, 0.2, 0.4) + 0.1 * sin(d * 10.0 - uTime * 2.0);
                    
                    gl_FragColor = vec4(color, 1.0);
                }
            `;

            {
                const renderer = new Renderer({
                    stencil: true, // 启用模版缓冲区
                });
                const gl = renderer.gl;
                document.body.appendChild(gl.canvas);
                gl.clearColor(0, 0, 0, 1);

                // 创建相机
                const camera = new Camera(gl, { fov: 45 });
                camera.position.set(0, 0, 5);

                // 创建轨道控制器
                const controls = new Orbit(camera, {
                    target: new Vec3(0, 0, 0),
                });

                function resize() {
                    renderer.setSize(window.innerWidth, window.innerHeight);
                    camera.perspective({ aspect: gl.canvas.width / gl.canvas.height });
                }
                window.addEventListener('resize', resize, false);
                resize();

                // 创建几何体
                const planeGeometry = new Plane(gl);
                const boxGeometry = new Box(gl);

                // 创建遮罩程序（用于写入模版缓冲区）
                const maskProgram = new Program(gl, {
                    vertex,
                    fragment: maskFragment,
                    depthWrite: false,
                });

                // 设置模版测试配置
                maskProgram.setStencilFunc(gl.ALWAYS, 1, 0xff);
                maskProgram.setStencilOp(gl.KEEP, gl.KEEP, gl.REPLACE);

                // 创建彩色程序（只在模版值为1的地方渲染）
                const colorProgram = new Program(gl, {
                    vertex,
                    fragment: colorFragment,
                    uniforms: {
                        uTime: { value: 0 },
                        uColor: { value: new Color(0.8, 0.3, 0.5) },
                    },
                });

                // 设置模版测试配置：只在模版值等于1的地方渲染
                colorProgram.setStencilFunc(gl.EQUAL, 1, 0xff);
                colorProgram.setStencilOp(gl.KEEP, gl.KEEP, gl.KEEP);

                // 创建背景程序（在模版值不等于1的地方渲染）
                const backgroundProgram = new Program(gl, {
                    vertex,
                    fragment: backgroundFragment,
                    uniforms: {
                        uTime: { value: 0 },
                    },
                });

                // 设置模版测试配置：只在模版值不等于1的地方渲染
                backgroundProgram.setStencilFunc(gl.NOTEQUAL, 1, 0xff);
                backgroundProgram.setStencilOp(gl.KEEP, gl.KEEP, gl.KEEP);

                // 创建场景对象
                const scene = new Transform();

                // 创建遮罩对象（旋转的立方体）
                const maskCube = new Mesh(gl, { geometry: boxGeometry, program: maskProgram });
                maskCube.setParent(scene);

                // 创建彩色平面（只在遮罩区域显示）
                const colorPlane = new Mesh(gl, { geometry: planeGeometry, program: colorProgram });
                colorPlane.position.z = -1;
                colorPlane.scale.set(3);
                colorPlane.setParent(scene);

                // 创建背景平面（在非遮罩区域显示）
                const backgroundPlane = new Mesh(gl, { geometry: planeGeometry, program: backgroundProgram });
                backgroundPlane.position.z = -2;
                backgroundPlane.scale.set(4);
                backgroundPlane.setParent(scene);

                requestAnimationFrame(update);
                function update(t) {
                    requestAnimationFrame(update);

                    const time = t * 0.001;

                    // 更新轨道控制器
                    controls.update();

                    // 更新uniform
                    colorProgram.uniforms.uTime.value = time;
                    backgroundProgram.uniforms.uTime.value = time;

                    // 旋转遮罩立方体
                    maskCube.rotation.x = time * 0.5;
                    maskCube.rotation.y = time * 0.7;

                    // 清除缓冲区（包括模版缓冲区）
                    gl.clear(gl.COLOR_BUFFER_BIT | gl.DEPTH_BUFFER_BIT | gl.STENCIL_BUFFER_BIT);

                    // 第一步：渲染遮罩到模版缓冲区
                    // 禁用颜色写入，只写入模版缓冲区
                    gl.colorMask(false, false, false, false);
                    renderer.render({ scene: maskCube, camera });

                    // 恢复颜色写入
                    gl.colorMask(true, true, true, true);

                    // 第二步：渲染彩色内容（只在遮罩区域）
                    renderer.render({ scene: colorPlane, camera, clear: false });

                    // 第三步：渲染背景（在非遮罩区域）
                    renderer.render({ scene: backgroundPlane, camera, clear: false });
                }
            }
        </script>
    </body>
</html>

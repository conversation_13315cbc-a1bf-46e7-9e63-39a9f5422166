<!DOCTYPE html>
<html lang="en">
    <head>
        <meta charset="UTF-8" />
        <meta http-equiv="X-UA-Compatible" content="IE=edge,chrome=1" />
        <meta name="viewport" content="width=device-width, minimal-ui, viewport-fit=cover, initial-scale=1.0, maximum-scale=1.0, minimum-scale=1.0, user-scalable=no" />
        <link rel="icon" type="image/png" href="assets/favicon.png" />

        <title>OGL • Stencil Mirror</title>
        <link href="assets/main.css" rel="stylesheet" />
    </head>
    <body>
        <div class="Info">
            Stencil Mirror - 模版缓冲区镜面反射<br />
            <small
                >使用模版缓冲区实现镜面反射效果<br />
                拖拽鼠标旋转视角，滚轮缩放，右键平移</small
            >
        </div>
        <script type="module">
            import { Renderer, Program, Color, Mesh, Plane, Box, Camera, Transform, Vec3, Mat4, Orbit } from '../src/index.js';

            // 基础顶点着色器
            const vertex = /* glsl */ `
                attribute vec3 position;
                attribute vec3 normal;

                uniform mat4 modelViewMatrix;
                uniform mat4 projectionMatrix;
                uniform mat3 normalMatrix;

                varying vec3 vNormal;
                varying vec3 vPosition;

                void main() {
                    vNormal = normalize(normalMatrix * normal);
                    vPosition = (modelViewMatrix * vec4(position, 1.0)).xyz;
                    gl_Position = projectionMatrix * modelViewMatrix * vec4(position, 1.0);
                }
            `;

            // 物体着色器
            const objectFragment = /* glsl */ `
                precision highp float;

                uniform vec3 uColor;
                uniform vec3 uLightPosition;
                
                varying vec3 vNormal;
                varying vec3 vPosition;

                void main() {
                    vec3 lightDir = normalize(uLightPosition - vPosition);
                    float diff = max(dot(vNormal, lightDir), 0.0);
                    
                    vec3 color = uColor * (0.3 + 0.7 * diff);
                    gl_FragColor = vec4(color, 1.0);
                }
            `;

            // 镜面着色器
            const mirrorFragment = /* glsl */ `
                precision highp float;
                
                void main() {
                    gl_FragColor = vec4(0.8, 0.8, 0.9, 0.3);
                }
            `;

            {
                const renderer = new Renderer({
                    stencil: true,
                    alpha: true,
                });
                const gl = renderer.gl;
                document.body.appendChild(gl.canvas);
                gl.clearColor(0.1, 0.1, 0.2, 1);

                // 创建相机
                const camera = new Camera(gl, { fov: 45 });
                camera.position.set(0, 2, 6);

                // 创建轨道控制器
                const controls = new Orbit(camera, {
                    target: new Vec3(0, 0.5, 0),
                });

                function resize() {
                    renderer.setSize(window.innerWidth, window.innerHeight);
                    camera.perspective({ aspect: gl.canvas.width / gl.canvas.height });
                }
                window.addEventListener('resize', resize, false);
                resize();

                // 创建几何体
                const planeGeometry = new Plane(gl);
                const boxGeometry = new Box(gl);

                // 光源位置
                const lightPosition = new Vec3(3, 3, 3);

                // 创建物体程序
                const objectProgram = new Program(gl, {
                    vertex,
                    fragment: objectFragment,
                    uniforms: {
                        uColor: { value: new Color(0.8, 0.3, 0.3) },
                        uLightPosition: { value: lightPosition },
                    },
                });

                // 创建反射物体程序
                const reflectedObjectProgram = new Program(gl, {
                    vertex,
                    fragment: objectFragment,
                    uniforms: {
                        uColor: { value: new Color(0.8, 0.3, 0.3) },
                        uLightPosition: { value: lightPosition },
                    },
                    cullFace: gl.FRONT, // 反射时翻转面
                });

                // 设置反射物体的模版测试
                reflectedObjectProgram.setStencilFunc(gl.EQUAL, 1, 0xff);
                reflectedObjectProgram.setStencilOp(gl.KEEP, gl.KEEP, gl.KEEP);

                // 创建镜面程序
                const mirrorProgram = new Program(gl, {
                    vertex,
                    fragment: mirrorFragment,
                    transparent: true,
                });

                // 设置镜面的模版测试（写入模版缓冲区）
                mirrorProgram.setStencilFunc(gl.ALWAYS, 1, 0xff);
                mirrorProgram.setStencilOp(gl.KEEP, gl.KEEP, gl.REPLACE);

                // 创建场景对象
                const scene = new Transform();

                // 创建物体（立方体）
                const cube = new Mesh(gl, { geometry: boxGeometry, program: objectProgram });
                cube.position.set(0, 1, 0);
                cube.setParent(scene);

                // 创建反射物体
                const reflectedCube = new Mesh(gl, { geometry: boxGeometry, program: reflectedObjectProgram });
                reflectedCube.setParent(scene);

                // 创建镜面（地面）
                const mirror = new Mesh(gl, { geometry: planeGeometry, program: mirrorProgram });
                mirror.rotation.x = -Math.PI / 2;
                mirror.scale.setScalar(5);
                mirror.setParent(scene);

                // 反射矩阵（沿Y=0平面反射）
                const reflectionMatrix = new Mat4();
                reflectionMatrix.set(1, 0, 0, 0, 0, -1, 0, 0, 0, 0, 1, 0, 0, 0, 0, 1);

                requestAnimationFrame(update);
                function update(t) {
                    requestAnimationFrame(update);

                    const time = t * 0.001;

                    // 更新轨道控制器
                    controls.update();

                    // 旋转立方体
                    cube.rotation.x = time * 0.5;
                    cube.rotation.y = time * 0.7;

                    // 更新反射立方体的变换
                    reflectedCube.matrix.copy(cube.worldMatrix);
                    reflectedCube.matrix.multiply(reflectionMatrix);
                    reflectedCube.rotation.x = cube.rotation.x;
                    reflectedCube.rotation.y = cube.rotation.y;
                    reflectedCube.position.set(cube.position.x, -cube.position.y, cube.position.z);

                    // 清除缓冲区
                    gl.clear(gl.COLOR_BUFFER_BIT | gl.DEPTH_BUFFER_BIT | gl.STENCIL_BUFFER_BIT);

                    // 第一步：渲染正常物体
                    renderer.render({ scene: cube, camera });

                    // 第二步：渲染镜面到模版缓冲区
                    // 禁用深度写入，允许后续反射物体渲染
                    gl.depthMask(false);
                    renderer.render({ scene: mirror, camera, clear: false });
                    gl.depthMask(true);

                    // 第三步：渲染反射物体（只在镜面区域）
                    renderer.render({ scene: reflectedCube, camera, clear: false });

                    // 第四步：再次渲染镜面（半透明效果）
                    gl.depthMask(false);
                    renderer.render({ scene: mirror, camera, clear: false });
                    gl.depthMask(true);
                }
            }
        </script>
    </body>
</html>
